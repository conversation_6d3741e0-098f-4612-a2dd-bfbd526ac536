import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { <PERSON>rowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import RegistrationPage from '../RegistrationPage';
import authReducer from '../../store/auth.slice';

// Mock the auth service
jest.mock('../../services/auth.service');

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        user: null,
        token: null,
        status: 'idle' as const,
        error: null,
        ...initialState,
      },
    },
  });
};

const renderWithProviders = (component: React.ReactElement, initialState = {}) => {
  const store = createMockStore(initialState);
  return {
    ...render(
      <Provider store={store}>
        <BrowserRouter>
          {component}
        </BrowserRouter>
      </Provider>
    ),
    store,
  };
};

describe('RegistrationPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render registration form with all required fields', () => {
    renderWithProviders(<RegistrationPage />);

    expect(screen.getByRole('heading', { name: /register/i })).toBeInTheDocument();
    expect(screen.getByLabelText(/first name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/last name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    // Check password fields exist by checking input count
    const inputs = screen.getAllByRole('textbox');
    expect(inputs.length).toBeGreaterThanOrEqual(3); // At least firstName, lastName, email
    expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/user type/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /register/i })).toBeInTheDocument();
    expect(screen.getByText(/already have an account/i)).toBeInTheDocument();
  });

  it('should show validation errors for empty required fields', async () => {
    const user = userEvent.setup();
    renderWithProviders(<RegistrationPage />);

    const submitButton = screen.getByRole('button', { name: /register/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('First name is required')).toBeInTheDocument();
    }, { timeout: 10000 });

    await waitFor(() => {
      expect(screen.getByText('Last name is required')).toBeInTheDocument();
      expect(screen.getByText('Email is required')).toBeInTheDocument();
      expect(screen.getByText('Password is required')).toBeInTheDocument();
      expect(screen.getByText('Please confirm your password')).toBeInTheDocument();
      expect(screen.getByText('Please select a user type')).toBeInTheDocument();
    });
  }, 15000);

  it('should show validation error for invalid email format', async () => {
    const user = userEvent.setup();
    renderWithProviders(<RegistrationPage />);

    const emailInput = screen.getByLabelText(/email/i);
    await user.type(emailInput, 'invalid-email');

    const submitButton = screen.getByRole('button', { name: /register/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter a valid email')).toBeInTheDocument();
    }, { timeout: 10000 });
  }, 15000);

  it('should show validation error for password mismatch', async () => {
    const user = userEvent.setup();
    renderWithProviders(<RegistrationPage />);

    const passwordInput = document.querySelector('input[name="password"]') as HTMLInputElement;
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

    await user.type(passwordInput, 'password123');
    await user.type(confirmPasswordInput, 'password456');

    const submitButton = screen.getByRole('button', { name: /register/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Passwords do not match')).toBeInTheDocument();
    });
  });

  it('should show validation error for short password', async () => {
    const user = userEvent.setup();
    renderWithProviders(<RegistrationPage />);

    const passwordInput = document.querySelector('input[name="password"]') as HTMLInputElement;
    await user.type(passwordInput, '123');

    const submitButton = screen.getByRole('button', { name: /register/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Password must be at least 6 characters')).toBeInTheDocument();
    });
  });

  it('should submit form with valid data', async () => {
    const user = userEvent.setup();
    const { store } = renderWithProviders(<RegistrationPage />);

    // Fill out the form
    await user.type(screen.getByLabelText(/first name/i), 'John');
    await user.type(screen.getByLabelText(/last name/i), 'Doe');
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(document.querySelector('input[name="password"]') as HTMLInputElement, 'password123');
    await user.type(screen.getByLabelText(/confirm password/i), 'password123');
    
    // Select user type
    const userTypeSelect = screen.getByLabelText(/user type/i);
    await user.click(userTypeSelect);
    await user.click(screen.getByText('Teacher'));

    const submitButton = screen.getByRole('button', { name: /register/i });
    await user.click(submitButton);

    // Check that the form submission was attempted
    await waitFor(() => {
      const state = store.getState();
      expect(state.auth.status).toBe('loading');
    });
  });

  it('should show loading state during registration', () => {
    renderWithProviders(<RegistrationPage />, { status: 'loading' });

    const submitButton = screen.getByRole('button', { name: /register/i });
    expect(submitButton).toBeDisabled();
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('should show error message on registration failure', () => {
    renderWithProviders(<RegistrationPage />, { 
      status: 'failed', 
      error: 'User with this email already exists' 
    });

    expect(screen.getByText(/user with this email already exists/i)).toBeInTheDocument();
  });

  it('should redirect to login page on successful registration', () => {
    renderWithProviders(<RegistrationPage />, { 
      status: 'succeeded',
      user: {
        _id: '123',
        email: '<EMAIL>',
        userType: 'Teacher',
        name: { first: 'John', last: 'Doe' },
        accountStatus: 'Active',
        createdAt: '2023-01-01',
        updatedAt: '2023-01-01'
      }
    });

    expect(screen.getByText(/registration successful/i)).toBeInTheDocument();
  });

  it('should have link to login page', () => {
    renderWithProviders(<RegistrationPage />);

    const loginLink = screen.getByRole('link', { name: /sign in/i });
    expect(loginLink).toBeInTheDocument();
    expect(loginLink).toHaveAttribute('href', '/login');
  });

  it('should clear error when user starts typing', async () => {
    const user = userEvent.setup();
    renderWithProviders(<RegistrationPage />, { 
      status: 'failed', 
      error: 'Registration failed' 
    });

    expect(screen.getByText(/registration failed/i)).toBeInTheDocument();

    const emailInput = screen.getByLabelText(/email/i);
    await user.type(emailInput, 'a');

    await waitFor(() => {
      expect(screen.queryByText(/registration failed/i)).not.toBeInTheDocument();
    });
  });
});
